# Supabase Configuration
SUPABASE_URL=https://qjriwcvexqvqvtegeokv.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFqcml3Y3ZleHF2cXZ0ZWdlb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3MTA3MjUsImV4cCI6MjA2MDI4NjcyNX0.qsAQ0DfTUwXBZb0BPLWa9XP1mqrhtkjzAxts_l9wyak
SUPABASE_JWT_SECRET=test-secret-key

# MongoDB Configuration
MONGODB_URI=mongodb+srv://akash:<EMAIL>/?retryWrites=true&w=majority&appName=user
MONGODB_DATABASE=agent_memory

# Vedant's Agent API
AGENT_API_URL=http://192.168.0.79:8003/memory/ask-agent
AGENT_API_KEY=memory_api_key_dev

# Optional TTS Configuration
TTS_ENABLED=false
TTS_API_URL=your_tts_api_url_here

# API Keys for LLM services
GROQ_API_KEY=********************************************************
