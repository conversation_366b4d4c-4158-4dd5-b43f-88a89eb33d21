[{"goals": [{"id": 1, "name": "Emergency Fund", "target": 10000, "cumulative_savings": 8000, "allocation": 500, "status": "behind", "adjustment": -100}, {"id": 2, "name": "Retirement Savings", "target": 20000, "cumulative_savings": 15000, "allocation": 1000, "status": "on_track", "adjustment": 0}, {"id": 3, "name": "Major Purchase", "target": 5000, "cumulative_savings": 3000, "allocation": 200, "status": "ahead", "adjustment": 50}], "month": 1}, {"goals": [{"id": 1, "name": "Emergency Fund", "target": 10000, "cumulative_savings": 8500, "allocation": 500, "status": "behind", "adjustment": -50, "trend": "improving"}, {"id": 2, "name": "Retirement Savings", "target": 20000, "cumulative_savings": 16000, "allocation": 1000, "status": "on_track", "adjustment": 0, "trend": "stagnant"}, {"id": 3, "name": "Major Purchase", "target": 5000, "cumulative_savings": 3250, "allocation": 200, "status": "ahead", "adjustment": 25, "trend": "improving"}], "month": 2}, {"goals": [{"id": 1, "name": "Emergency Fund", "target": 10000, "cumulative_savings": 9000, "allocation": 500, "status": "behind", "adjustment": -25, "trend": "improving"}, {"id": 2, "name": "Retirement Savings", "target": 20000, "cumulative_savings": 17000, "allocation": 1000, "status": "on_track", "adjustment": 50, "trend": "improving"}, {"id": 3, "name": "Major Purchase", "target": 5000, "cumulative_savings": 3750}], "month": 3}, {"goals": [{"id": 1, "name": "Emergency Fund", "target": 10000, "cumulative_savings": 9500, "allocation": 500, "status": "behind", "adjustment": -20, "trend": "improving"}, {"id": 2, "name": "Retirement Savings", "target": 20000, "cumulative_savings": 18000, "allocation": 1000, "status": "on_track", "adjustment": 60, "trend": "improving"}, {"id": 3, "name": "Major Purchase", "target": 5000, "cumulative_savings": 4250, "allocation": 750, "status": "behind", "adjustment": 25, "trend": "stagnant"}], "month": 4}, {"goals": [{"id": 1, "name": "Emergency Fund", "target": 10000, "cumulative_savings": 10000, "allocation": 500, "status": "on_track", "adjustment": 0, "trend": "improving"}, {"id": 2, "name": "Retirement Savings", "target": 20000, "cumulative_savings": 19000, "allocation": 1000, "status": "on_track", "adjustment": 40, "trend": "improving"}, {"id": 3, "name": "Major Purchase", "target": 5000, "cumulative_savings": 4500, "allocation": 750, "status": "behind", "adjustment": 50, "trend": "improving"}], "month": 5}, {"goals": [{"id": 1, "name": "Emergency Fund", "target": 10000, "cumulative_savings": 10500, "allocation": 500, "status": "ahead", "adjustment": -50, "trend": "improving"}, {"id": 2, "name": "Retirement Savings", "target": 20000, "cumulative_savings": 19400, "allocation": 1000, "status": "on_track", "adjustment": 20, "trend": "improving"}, {"id": 3, "name": "Major Purchase", "target": 5000, "cumulative_savings": 4750, "allocation": 750, "status": "behind", "adjustment": 25, "trend": "improving"}], "month": 6}]