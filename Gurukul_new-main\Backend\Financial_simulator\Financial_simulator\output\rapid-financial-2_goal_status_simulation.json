[{"goals": {"emergency_fund": {"target": 10000, "cumulative_savings": 8000, "allocation": 500, "status": "behind", "adjustment": "increase allocation by 10%"}, "retirement_savings": {"target": 50000, "cumulative_savings": 35000, "allocation": 1000, "status": "on_track", "adjustment": "maintain current allocation"}, "vacation_fund": {"target": 5000, "cumulative_savings": 4000, "allocation": 200, "status": "ahead", "adjustment": "reduce allocation by 5%"}}, "trends": {"overall_progress": "improving", "average_allocation": 570, "average_savings_rate": 0.5}, "recommendations": {"review_expenses": true, "rebalance_allocations": true}, "month": 1}, {"month": 2, "timestamp": "2025-06-24T15:08:19.795283", "goals": {"emergency_fund": {"target": 10000.0, "current": 6500.0, "progress_percentage": 65.0, "monthly_contribution": 500.0, "estimated_completion": "Month 9", "status": "on_track"}, "vacation_fund": {"target": 2000.0, "current": 1200.0, "progress_percentage": 60.0, "monthly_contribution": 200.0, "estimated_completion": "Month 6", "status": "on_track"}, "retirement_savings": {"target": 5000.0, "current": 2000.0, "progress_percentage": 40.0, "monthly_contribution": 300.0, "estimated_completion": "Month 12", "status": "on_track"}}, "trends": {"emergency_fund": "improving", "vacation_fund": "steady", "retirement_savings": "improving"}, "overall_progress": "Good progress across all financial goals", "recommendations": ["Consider increasing retirement contributions by 5%", "Maintain current emergency fund contribution rate"]}]