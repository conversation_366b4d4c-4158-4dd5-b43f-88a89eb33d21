[{"user_name": "Concurrent Test User", "month": 1, "income": {"salary": 75000.0, "investments": 0, "other": 0, "total": 75000.0}, "expenses": {"housing": 2000.0, "utilities": 0, "groceries": 800.0, "transportation": 500.0, "healthcare": 0, "entertainment": 0, "dining_out": 0, "subscriptions": 0, "other": 0, "total": 3300.0}, "balance": {"starting": 0, "ending": 71700.0, "change": 71700.0}}, {"month": 2, "timestamp": "2025-06-24T14:56:09.776245", "user_name": "Concurrent Test User", "income": {"salary": 75000.0, "investments": 0, "other": 0, "total": 75000.0}, "expenses": {"housing": 0, "utilities": 0, "groceries": 0, "transportation": 0, "healthcare": 0, "entertainment": 0, "dining_out": 0, "subscriptions": 0, "other": 0, "total": 3300.0}, "savings": {"amount": 71700.0, "percentage_of_income": 95.6, "target_met": false}, "balance": {"starting": 0, "ending": 71700.0, "change": 71700.0}, "analysis": {"spending_categories": {"essential": 0, "non_essential": 0, "ratio": 0}, "savings_rate": "N/A", "cash_flow": "Neutral"}, "notes": "This is a minimal report based on your provided data."}, {"month": 3, "timestamp": "2025-06-24T14:58:39.080308", "user_name": "Concurrent Test User", "income": {"salary": 75000.0, "investments": 0, "other": 0, "total": 75000.0}, "expenses": {"housing": 0, "utilities": 0, "groceries": 0, "transportation": 0, "healthcare": 0, "entertainment": 0, "dining_out": 0, "subscriptions": 0, "other": 0, "total": 3300.0}, "savings": {"amount": 71700.0, "percentage_of_income": 95.6, "target_met": false}, "balance": {"starting": 0, "ending": 71700.0, "change": 71700.0}, "analysis": {"spending_categories": {"essential": 0, "non_essential": 0, "ratio": 0}, "savings_rate": "N/A", "cash_flow": "Neutral"}, "notes": "This is a minimal report based on your provided data."}, {"month": 4, "timestamp": "2025-06-24T15:01:24.981693", "user_name": "Concurrent Test User", "income": {"salary": 75000.0, "investments": 0, "other": 0, "total": 75000.0}, "expenses": {"housing": 0, "utilities": 0, "groceries": 0, "transportation": 0, "healthcare": 0, "entertainment": 0, "dining_out": 0, "subscriptions": 0, "other": 0, "total": 3300.0}, "savings": {"amount": 71700.0, "percentage_of_income": 95.6, "target_met": false}, "balance": {"starting": 0, "ending": 71700.0, "change": 71700.0}, "analysis": {"spending_categories": {"essential": 0, "non_essential": 0, "ratio": 0}, "savings_rate": "N/A", "cash_flow": "Neutral"}, "notes": "This is a minimal report based on your provided data."}, {"month": 5, "timestamp": "2025-06-24T15:03:39.641188", "user_name": "Concurrent Test User", "income": {"salary": 75000.0, "investments": 0, "other": 0, "total": 75000.0}, "expenses": {"housing": 0, "utilities": 0, "groceries": 0, "transportation": 0, "healthcare": 0, "entertainment": 0, "dining_out": 0, "subscriptions": 0, "other": 0, "total": 3300.0}, "savings": {"amount": 71700.0, "percentage_of_income": 95.6, "target_met": false}, "balance": {"starting": 0, "ending": 71700.0, "change": 71700.0}, "analysis": {"spending_categories": {"essential": 0, "non_essential": 0, "ratio": 0}, "savings_rate": "N/A", "cash_flow": "Neutral"}, "notes": "This is a minimal report based on your provided data."}, {"month": 6, "timestamp": "2025-06-24T15:05:17.256005", "user_name": "Concurrent Test User", "income": {"salary": 75000.0, "investments": 0, "other": 0, "total": 75000.0}, "expenses": {"housing": 0, "utilities": 0, "groceries": 0, "transportation": 0, "healthcare": 0, "entertainment": 0, "dining_out": 0, "subscriptions": 0, "other": 0, "total": 3300.0}, "savings": {"amount": 71700.0, "percentage_of_income": 95.6, "target_met": false}, "balance": {"starting": 0, "ending": 71700.0, "change": 71700.0}, "analysis": {"spending_categories": {"essential": 0, "non_essential": 0, "ratio": 0}, "savings_rate": "N/A", "cash_flow": "Neutral"}, "notes": "This is a minimal report based on your provided data."}]