name: CI/CD Pipeline

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:latest
        ports:
          - 27017:27017
      
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f Backend/requirements.txt ]; then pip install -r Backend/requirements.txt; fi
        pip install pytest pytest-cov
    
    - name: Run tests
      run: |
        cd Backend
        pytest --cov=. --cov-report=xml
      env:
        MONGODB_URL: mongodb://localhost:27017/
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        ENVIRONMENT: test
    
    - name: Upload coverage report
      uses: codecov/codecov-action@v3
      with:
        file: ./Backend/coverage.xml
        fail_ci_if_error: false

  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
        cache-dependency-path: 'new frontend/package-lock.json'
    
    - name: Install dependencies
      run: |
        cd "new frontend"
        npm ci
    
    - name: Run tests
      run: |
        cd "new frontend"
        npm test -- --coverage
    
    - name: Build
      run: |
        cd "new frontend"
        npm run build

  build-and-push-images:
    needs: [test-backend, test-frontend]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    
    - name: Build and push Backend image
      uses: docker/build-push-action@v4
      with:
        context: ./Backend
        push: true
        tags: ${{ secrets.DOCKERHUB_USERNAME }}/gurukul-backend:latest
    
    - name: Build and push Frontend image
      uses: docker/build-push-action@v4
      with:
        context: ./new frontend
        push: true
        tags: ${{ secrets.DOCKERHUB_USERNAME }}/gurukul-frontend:latest

  deploy-to-staging:
    needs: build-and-push-images
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install SSH key
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}
    
    - name: Deploy to staging server
      run: |
        ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "cd /opt/gurukul && \
        docker-compose pull && \
        docker-compose up -d"

  notify:
    needs: [deploy-to-staging]
    if: always()
    runs-on: ubuntu-latest
    
    steps:
    - name: Send Slack notification
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      if: always()